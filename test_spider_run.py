#!/usr/bin/env python3
"""
Simple test to run the Xueqiu spider and see if it works
"""

import os
import sys
import subprocess
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def test_spider():
    """Test running the Xueqiu spider"""
    print("🕷️  Testing Xueqiu Media Spider...")
    
    # Change to the crawl directory
    crawl_dir = os.path.join(os.path.dirname(__file__), 'core', 'crawl')
    
    try:
        # Run the spider with scrapy crawl command (disable proxy for testing)
        cmd = [
            'scrapy', 'crawl', 'xueqiu-media',
            '-s', 'LOG_LEVEL=INFO',
            '-s', 'DOWNLOAD_DELAY=3',
            '-s', 'RANDOMIZE_DOWNLOAD_DELAY=True',
            '-s', 'PROXY_ENABLED=False',  # Disable proxy for testing
            '-L', 'INFO'
        ]
        
        print(f"Running command: {' '.join(cmd)}")
        print(f"Working directory: {crawl_dir}")
        
        # Run the command
        result = subprocess.run(
            cmd,
            cwd=crawl_dir,
            capture_output=True,
            text=True,
            timeout=120  # 2 minute timeout
        )
        
        print(f"\n📊 Spider execution completed with return code: {result.returncode}")
        
        if result.stdout:
            print("\n📝 STDOUT:")
            print(result.stdout)
        
        if result.stderr:
            print("\n⚠️  STDERR:")
            print(result.stderr)
        
        # Analyze the output
        if result.returncode == 0:
            print("\n✅ Spider completed successfully!")
        else:
            print(f"\n❌ Spider failed with return code: {result.returncode}")
            
        # Look for specific indicators in the output
        output_text = result.stdout + result.stderr
        
        if "Captcha detected" in output_text:
            print("🚫 Captcha detection found - anti-detection measures may need improvement")
        elif "400016" in output_text:
            print("🔐 Authentication error found - cookies may need to be refreshed")
        elif "items scraped" in output_text:
            print("📦 Items were successfully scraped!")
        elif "302" in output_text and "captcha" in output_text.lower():
            print("🚫 Redirected to captcha page - IP may be blocked")
        
    except subprocess.TimeoutExpired:
        print("⏰ Spider execution timed out after 2 minutes")
    except FileNotFoundError:
        print("❌ Scrapy not found. Make sure you're in the correct environment and scrapy is installed.")
    except Exception as e:
        print(f"❌ Error running spider: {e}")

def main():
    """Main function"""
    print("🧪 Testing improved Xueqiu spider...\n")
    test_spider()
    
    print("\n" + "="*60)
    print("📋 Next steps if spider fails:")
    print("1. Check if cookies in database are up-to-date")
    print("2. Verify network connectivity to xueqiu.com")
    print("3. Consider using proxy if IP is blocked")
    print("4. Check spider logs for specific error messages")

if __name__ == "__main__":
    main()
